{"name": "arches-kpi", "version": "0.0.0", "license": "MIT", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "prettier-fix": "prettier --write ."}, "dependencies": {"@google/generative-ai": "^0.21.0", "@icons-pack/react-simple-icons": "^10.2.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.6", "@stripe/stripe-js": "5.2.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "lucide-react": "^0.468.0", "next": "^15.0.4", "openai": "^4.76.3", "react": "^18.2.0", "react-day-picker": "^9.4.4", "react-dom": "^18.2.0", "react-merge-refs": "^2.1.1", "stripe": "^17.4.0", "tailwind-merge": "^2.5.5", "tailwindcss": "^3.4.16", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^22.10.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-config-next": "^15.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-tailwindcss": "^3.17.5", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "supabase": "^2.0.0", "typescript": "^5.7.2"}}