# Troubleshooting Guide - Chat with DB AI Agent

This guide helps you resolve common issues when deploying and running the Chat with DB AI Agent on Google Cloud Run.

## Common Error: "Connection error"

### Symptoms
- Error message: `❌ Error generating SQL: Connection error.`
- Application works locally but fails in Cloud Run
- BigQuery schema fetching works but SQL generation fails

### Root Causes and Solutions

#### 1. Missing Environment Variables

**Problem**: Required environment variables are not set in Cloud Run.

**Solution**: 
```bash
# Set the OpenAI API key before deployment
export OPENAI_API_KEY=your_openai_api_key_here

# Use the updated deployment script
./setup-env-and-deploy.sh
```

**Manual fix for existing deployment**:
```bash
gcloud run services update chat-with-db \
    --region us-central1 \
    --set-env-vars "OPENAI_API_KEY=your_key_here,GOOGLE_CLOUD_PROJECT=ais-prod-440309"
```

#### 2. Service Account Permissions

**Problem**: Service account lacks BigQuery permissions.

**Solution**:
```bash
# Grant BigQuery permissions
gcloud projects add-iam-policy-binding ais-prod-440309 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.dataViewer"

gcloud projects add-iam-policy-binding ais-prod-440309 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.jobUser"
```

#### 3. OpenAI API Key Issues

**Problem**: Invalid or expired OpenAI API key.

**Check**:
```bash
# Test your API key locally
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $OPENAI_API_KEY"
```

**Solution**: Get a new API key from https://platform.openai.com/api-keys

#### 4. Network/Firewall Issues

**Problem**: Cloud Run cannot reach OpenAI API.

**Solution**: Ensure Cloud Run has internet access (default configuration should work).

## Debugging Steps

### 1. Check Cloud Run Logs
```bash
# View recent logs
gcloud logs read --project=ais-prod-440309 --service=chat-with-db --limit=50

# Follow logs in real-time
gcloud logs tail --project=ais-prod-440309 --service=chat-with-db
```

### 2. Test Health Endpoint
```bash
SERVICE_URL=$(gcloud run services describe chat-with-db --region=us-central1 --format='value(status.url)')
curl $SERVICE_URL/health
```

### 3. Check Environment Variables
```bash
# List current environment variables
gcloud run services describe chat-with-db --region=us-central1 --format='export' | grep env
```

### 4. Test BigQuery Connection
```bash
# Test with a simple question
curl -X POST $SERVICE_URL/api/chat/ask \
  -H "Content-Type: application/json" \
  -d '{"question":"Show me a simple test query","db_type":"bigquery"}'
```

## Quick Fixes

### Reset ChromaDB (if schema issues)
```bash
curl -X POST $SERVICE_URL/reset-chromadb
```

### Redeploy with Fresh Environment
```bash
# Set your API key
export OPENAI_API_KEY=your_key_here

# Run the complete setup and deployment
./setup-env-and-deploy.sh
```

### Force New Deployment
```bash
# Trigger a new deployment with updated environment
gcloud run deploy chat-with-db \
    --image gcr.io/ais-prod-440309/chat-with-db \
    --region us-central1 \
    --set-env-vars "OPENAI_API_KEY=$OPENAI_API_KEY,GOOGLE_CLOUD_PROJECT=ais-prod-440309" \
    --service-account "<EMAIL>"
```

## Prevention

### 1. Always Set Environment Variables Before Deployment
```bash
export OPENAI_API_KEY=your_key_here
./deploy-cloud-run.sh
```

### 2. Use the Setup Script
The `setup-env-and-deploy.sh` script handles all configuration automatically:
```bash
export OPENAI_API_KEY=your_key_here
./setup-env-and-deploy.sh
```

### 3. Verify Deployment
After deployment, always test:
```bash
# Health check
curl $SERVICE_URL/health

# API test
curl -X POST $SERVICE_URL/api/chat/ask \
  -H "Content-Type: application/json" \
  -d '{"question":"Test query","db_type":"bigquery"}'
```

## Getting Help

If you're still experiencing issues:

1. **Check the logs**: `gcloud logs read --project=ais-prod-440309 --service=chat-with-db --limit=100`
2. **Verify API key**: Test it with OpenAI directly
3. **Check permissions**: Ensure service account has BigQuery access
4. **Try local deployment**: Test with Docker locally first

## Contact

For additional support, check the application logs and ensure all environment variables are properly set.
